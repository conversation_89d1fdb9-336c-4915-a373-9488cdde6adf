package dex

import (
	"context"
	"fmt"
	"log"
	"math/big"
	"strings"

	"unified-tx-parser/pkg/core"
	"unified-tx-parser/pkg/model"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
)

// Uniswap协议常量
const (
	// Uniswap V2 合约地址
	uniswapV2RouterAddr  = "******************************************"
	uniswapV2FactoryAddr = "******************************************"

	// Uniswap V3 合约地址
	uniswapV3RouterAddr  = "******************************************"
	uniswapV3Router2Addr = "******************************************"
	uniswapV3FactoryAddr = "******************************************"

	// 事件签名哈希
	swapV2EventSig      = "0xd78ad95fa46c994b6551d0da85fc275fe613ce37657fb8d5e3d130840159d822"
	swapV3EventSig      = "0xc42079f94a6350d7e6235f29174924f928cc2ac818eb64fed8004e115fbcca67"
	mintV2EventSig      = "0x4c209b5fc8ad50758f13e2e1088ba56a560dff690a1c6fef26394f4c03821c4f"
	burnV2EventSig      = "0xdccd412f0b1252819cb1fd330b93224ca42612892bb3f4f789976e6d81936496"
	mintV3EventSig      = "0x7a53080ba414158be7ec69b987b5fb7d07dee101fe85488f0853ae16239d0bde"
	burnV3EventSig      = "0x0c396cd989a39f4459b5fa1aed6a9a8dcdbc45908acfd67e028cd568da98982c"
	pairCreatedEventSig = "0x0d3648bd0f6ba80134a33ba9275ac585d9d315f0ad8355cddefde31afa28d0e9"
	poolCreatedEventSig = "0x783cca1c0412dd0d695e784568c96da2e9c22ff989357a2e8b1d9b2b4e6b7118"
)

// UniswapExtractor Uniswap DEX数据提取器 - 简化的自包含实现
type UniswapExtractor struct {
	// 移除handler依赖，直接在此类中实现所有逻辑
}

// NewUniswapExtractor 创建Uniswap提取器
func NewUniswapExtractor() *UniswapExtractor {
	return &UniswapExtractor{}
}

// GetSupportedProtocols 获取支持的协议
func (u *UniswapExtractor) GetSupportedProtocols() []string {
	return []string{"uniswap", "uniswap-v2", "uniswap-v3"}
}

// GetSupportedChains 获取支持的链类型
func (u *UniswapExtractor) GetSupportedChains() []core.ChainType {
	return []core.ChainType{core.ChainTypeEthereum, core.ChainTypeBSC}
}

// ExtractDexData 从统一区块数据中提取Uniswap DEX相关数据 - 简化实现
func (u *UniswapExtractor) ExtractDexData(ctx context.Context, blocks []core.UnifiedBlock) (*core.DexData, error) {
	dexData := &core.DexData{
		Pools:        make([]model.Pool, 0),
		Transactions: make([]model.Transaction, 0),
		Liquidities:  make([]model.Liquidity, 0),
		Reserves:     make([]model.Reserve, 0),
		Tokens:       make([]model.Token, 0),
	}

	// 遍历所有区块
	for _, block := range blocks {
		// 只处理以太坊和BSC链的区块
		if !u.isSupported(block.ChainType) {
			continue
		}

		log.Printf("🦄 [Uniswap] 处理区块 %s，包含 %d 个交易", block.BlockNumber.String(), len(block.Transactions))

		// 遍历区块中的所有交易
		for i, tx := range block.Transactions {
			// 直接从交易的原始数据中提取以太坊日志
			ethLogs := u.extractEthereumLogsFromTransaction(&tx)
			log.Printf("🦄 [Uniswap] 交易 %d/%d (hash: %s) 提取到 %d 个日志", i+1, len(block.Transactions), tx.TxHash, len(ethLogs))

			if len(ethLogs) == 0 {
				continue
			}

			// 处理每个日志
			uniswapLogCount := 0
			for _, ethLog := range ethLogs {
				if !u.isUniswapLog(ethLog) {
					continue
				}
				uniswapLogCount++

				// 根据日志类型处理
				logType := u.getLogType(ethLog)
				log.Printf("🦄 [Uniswap] 发现Uniswap日志，类型: %s, 地址: %s", logType, ethLog.Address.Hex())

				switch logType {
				case "swap":
					if modelTx := u.createTransactionFromLog(ethLog, &tx); modelTx != nil {
						dexData.Transactions = append(dexData.Transactions, *modelTx)
						log.Printf("🦄 [Uniswap] 创建交易记录: %s", modelTx.Hash)
					}

				case "mint":
					if liquidity := u.enhanceCreateLiquidityFromLog(ethLog, &tx, "add"); liquidity != nil {
						dexData.Liquidities = append(dexData.Liquidities, *liquidity)
						log.Printf("🦄 [Uniswap] 创建流动性记录 (add): %s", liquidity.Hash)
					}

				case "burn":
					if liquidity := u.enhanceCreateLiquidityFromLog(ethLog, &tx, "remove"); liquidity != nil {
						dexData.Liquidities = append(dexData.Liquidities, *liquidity)
						log.Printf("🦄 [Uniswap] 创建流动性记录 (remove): %s", liquidity.Hash)
					}

				case "pair_created", "pool_created":
					if pool := u.createPoolFromLog(ethLog, &tx); pool != nil {
						dexData.Pools = append(dexData.Pools, *pool)
						log.Printf("🦄 [Uniswap] 创建池子记录: %s", pool.Addr)
					}
				}
			}

			if uniswapLogCount > 0 {
				log.Printf("🦄 [Uniswap] 交易 %s 处理了 %d 个Uniswap日志", tx.TxHash, uniswapLogCount)
			}
		}
	}

	return dexData, nil
}

// SupportsBlock 检查是否支持该区块 - 简化实现
func (u *UniswapExtractor) SupportsBlock(block *core.UnifiedBlock) bool {
	// 只支持以太坊和BSC链
	if !u.isSupported(block.ChainType) {
		return false
	}

	// 检查区块中是否有任何交易包含Uniswap日志
	for _, tx := range block.Transactions {
		ethLogs := u.extractEthereumLogsFromTransaction(&tx)
		for _, log := range ethLogs {
			if u.isUniswapLog(log) {
				return true
			}
		}
	}
	return false
}

// isSupported 检查是否支持该链类型
func (u *UniswapExtractor) isSupported(chainType core.ChainType) bool {
	return chainType == core.ChainTypeEthereum || chainType == core.ChainTypeBSC
}

// extractEthereumLogsFromTransaction 从交易中提取以太坊日志
func (u *UniswapExtractor) extractEthereumLogsFromTransaction(tx *core.UnifiedTransaction) []*types.Log {
	// 根据不同的原始数据类型处理
	switch rawData := tx.RawData.(type) {
	case map[string]interface{}:
		// 首先尝试直接获取logs字段
		if logs, ok := rawData["logs"]; ok {
			return u.parseLogsFromInterface(logs)
		}

		// 如果没有直接的logs字段，尝试从receipt中获取
		if receipt, ok := rawData["receipt"]; ok {
			if ethReceipt, ok := receipt.(*types.Receipt); ok {
				return ethReceipt.Logs
			}
		}
	}
	return nil
}

// parseLogsFromInterface 解析日志接口
func (u *UniswapExtractor) parseLogsFromInterface(logs interface{}) []*types.Log {
	switch logsData := logs.(type) {
	case []interface{}:
		result := make([]*types.Log, 0, len(logsData))
		for _, log := range logsData {
			if logMap, ok := log.(map[string]interface{}); ok {
				if ethLog := u.convertToEthLog(logMap); ethLog != nil {
					result = append(result, ethLog)
				}
			}
		}
		return result
	default:
		return nil
	}
}

// convertToEthLog 将map转换为以太坊日志
func (u *UniswapExtractor) convertToEthLog(logMap map[string]interface{}) *types.Log {
	log := &types.Log{}

	if address, ok := logMap["address"].(string); ok {
		log.Address = common.HexToAddress(address)
	}

	if topics, ok := logMap["topics"].([]interface{}); ok {
		log.Topics = make([]common.Hash, len(topics))
		for i, topic := range topics {
			if topicStr, ok := topic.(string); ok {
				log.Topics[i] = common.HexToHash(topicStr)
			}
		}
	}

	if data, ok := logMap["data"].(string); ok {
		log.Data = common.FromHex(data)
	}

	return log
}

// parseSwapAmounts 从Swap事件日志中解析交易金额
func (u *UniswapExtractor) parseSwapAmounts(log *types.Log) (amountIn, amountOut *big.Int, tokenIn, tokenOut string) {
	if len(log.Topics) < 2 || len(log.Data) < 64 {
		return big.NewInt(0), big.NewInt(0), "", ""
	}

	// 根据不同版本的Swap事件解析
	topic0 := log.Topics[0].Hex()

	switch topic0 {
	case swapV2EventSig:
		// Uniswap V2 Swap事件解析
		if len(log.Data) >= 128 {
			// V2: Swap(sender, amount0In, amount1In, amount0Out, amount1Out, to)
			amount0In := new(big.Int).SetBytes(log.Data[0:32])
			amount1In := new(big.Int).SetBytes(log.Data[32:64])
			amount0Out := new(big.Int).SetBytes(log.Data[64:96])
			amount1Out := new(big.Int).SetBytes(log.Data[96:128])

			// 确定输入和输出金额
			if amount0In.Cmp(big.NewInt(0)) > 0 {
				amountIn = amount0In
				amountOut = amount1Out
			} else {
				amountIn = amount1In
				amountOut = amount0Out
			}
		}

	case swapV3EventSig:
		// Uniswap V3 Swap事件解析
		if len(log.Data) >= 160 {
			// V3: Swap(sender, recipient, amount0, amount1, sqrtPriceX96, liquidity, tick)
			amount0 := new(big.Int).SetBytes(log.Data[0:32])
			amount1 := new(big.Int).SetBytes(log.Data[32:64])

			// V3中amount可能为负数，需要处理
			if amount0.Sign() > 0 {
				amountIn = amount0
				amountOut = new(big.Int).Abs(amount1)
			} else {
				amountIn = new(big.Int).Abs(amount0)
				amountOut = amount1
			}
		}
	}

	// 如果解析失败，返回默认值
	if amountIn == nil {
		amountIn = big.NewInt(0)
	}
	if amountOut == nil {
		amountOut = big.NewInt(0)
	}

	return amountIn, amountOut, tokenIn, tokenOut
}

// calculatePrice 计算交易价格
func (u *UniswapExtractor) calculatePrice(amountIn, amountOut *big.Int) float64 {
	if amountIn == nil || amountOut == nil || amountIn.Cmp(big.NewInt(0)) == 0 {
		return 0.0
	}

	// 价格 = amountOut / amountIn
	amountInFloat := new(big.Float).SetInt(amountIn)
	amountOutFloat := new(big.Float).SetInt(amountOut)

	price := new(big.Float).Quo(amountOutFloat, amountInFloat)
	priceFloat64, _ := price.Float64()

	return priceFloat64
}

// isUniswapLog 检查是否是Uniswap日志
func (u *UniswapExtractor) isUniswapLog(log *types.Log) bool {
	if len(log.Topics) == 0 {
		return false
	}

	// 检查是否是Uniswap相关的事件签名
	topic0 := log.Topics[0].Hex()
	return topic0 == swapV2EventSig ||
		topic0 == swapV3EventSig ||
		topic0 == mintV2EventSig ||
		topic0 == burnV2EventSig ||
		topic0 == mintV3EventSig ||
		topic0 == burnV3EventSig ||
		topic0 == pairCreatedEventSig ||
		topic0 == poolCreatedEventSig
}

// getLogType 获取日志类型
func (u *UniswapExtractor) getLogType(log *types.Log) string {
	if len(log.Topics) == 0 {
		return ""
	}

	topic0 := log.Topics[0].Hex()
	switch topic0 {
	case swapV2EventSig, swapV3EventSig:
		return "swap"
	case mintV2EventSig, mintV3EventSig:
		return "mint"
	case burnV2EventSig, burnV3EventSig:
		return "burn"
	case pairCreatedEventSig:
		return "pair_created"
	case poolCreatedEventSig:
		return "pool_created"
	default:
		return ""
	}
}

// createTransactionFromLog 从日志创建交易记录
func (u *UniswapExtractor) createTransactionFromLog(log *types.Log, tx *core.UnifiedTransaction) *model.Transaction {
	poolAddr := log.Address.Hex()

	// 解析交易金额和代币信息
	amountIn, amountOut, tokenIn, tokenOut := u.parseSwapAmounts(log)

	// 计算价格
	price := u.calculatePrice(amountIn, amountOut)

	// 计算价值（简化为价格 * 输入金额）
	amountInFloat := new(big.Float).SetInt(amountIn)
	value, _ := new(big.Float).Mul(amountInFloat, big.NewFloat(price)).Float64()

	// 安全获取BlockNumber值
	var blockNumber int64
	if tx.BlockNumber != nil {
		blockNumber = tx.BlockNumber.Int64()
	}

	return &model.Transaction{
		Addr:        poolAddr,
		Router:      tx.ToAddress,
		Factory:     u.getFactoryAddress(log),
		Pool:        poolAddr,
		Hash:        tx.TxHash,
		From:        tx.FromAddress,
		Side:        "swap",
		Amount:      amountIn,
		Price:       price,
		Value:       value,
		Time:        uint64(tx.Timestamp.Unix()),
		EventIndex:  0,
		TxIndex:     int64(tx.TxIndex),
		SwapIndex:   0,
		BlockNumber: blockNumber,
		Extra: &model.TransactionExtra{
			QuoteAddr:     tokenOut,
			QuotePrice:    fmt.Sprintf("%.6f", price),
			Type:          "swap",
			TokenSymbol:   u.getTokenSymbol(tokenIn),
			TokenDecimals: 18, // 默认值，实际应该从合约获取
		},
	}
}

// createLiquidityFromLog 从日志创建流动性记录
func (u *UniswapExtractor) createLiquidityFromLog(log *types.Log, tx *core.UnifiedTransaction, side string) *model.Liquidity {
	poolAddr := log.Address.Hex()

	return &model.Liquidity{
		Addr:    poolAddr,
		Router:  tx.ToAddress,
		Factory: u.getFactoryAddress(log),
		Pool:    poolAddr,
		Hash:    tx.TxHash,
		From:    tx.FromAddress,
		Pos:     "",
		Side:    side,
		Amount:  big.NewInt(0), // 需要从日志数据中解析
		Value:   0.0,           // 需要计算
		Time:    uint64(tx.Timestamp.Unix()),
		Key:     tx.TxHash + "_" + side,
		Extra: &model.LiquidityExtra{
			Key:    tx.TxHash + "_" + side,
			Values: []float64{0.0},
			Time:   uint64(tx.Timestamp.Unix()),
		},
	}
}

// createPoolFromLog 从日志创建池子记录
func (u *UniswapExtractor) createPoolFromLog(log *types.Log, tx *core.UnifiedTransaction) *model.Pool {
	poolAddr := log.Address.Hex()

	// 解析代币地址和费率
	tokens, fee := u.parsePoolCreationData(log)

	return &model.Pool{
		Addr:     poolAddr,
		Factory:  u.getFactoryAddress(log),
		Protocol: "uniswap",
		Tokens:   tokens,
		Fee:      fee,
		Extra: &model.PoolExtra{
			Hash: tx.TxHash,
			From: tx.FromAddress,
			Time: uint64(tx.Timestamp.Unix()),
		},
	}
}

// parsePoolCreationData 解析池子创建数据
func (u *UniswapExtractor) parsePoolCreationData(log *types.Log) (map[int]string, int) {
	tokens := make(map[int]string)
	fee := 3000 // 默认费率

	if len(log.Topics) >= 3 {
		// PairCreated/PoolCreated事件通常包含token0和token1地址
		tokens[0] = common.BytesToAddress(log.Topics[1].Bytes()).Hex()
		tokens[1] = common.BytesToAddress(log.Topics[2].Bytes()).Hex()
	}

	// 对于V3，尝试从数据中解析费率
	topic0 := log.Topics[0].Hex()
	if topic0 == poolCreatedEventSig && len(log.Data) >= 32 {
		// V3 PoolCreated事件包含费率信息
		feeBytes := log.Data[0:32]
		if feeInt := new(big.Int).SetBytes(feeBytes); feeInt.IsInt64() {
			fee = int(feeInt.Int64())
		}
	}

	return tokens, fee
}

// getFactoryAddress 获取工厂地址
func (u *UniswapExtractor) getFactoryAddress(log *types.Log) string {
	// 根据日志类型返回对应的工厂地址
	if len(log.Topics) == 0 {
		return ""
	}

	topic0 := log.Topics[0].Hex()
	switch topic0 {
	case swapV2EventSig, mintV2EventSig, burnV2EventSig, pairCreatedEventSig:
		return uniswapV2FactoryAddr
	case swapV3EventSig, mintV3EventSig, burnV3EventSig, poolCreatedEventSig:
		return uniswapV3FactoryAddr
	default:
		return ""
	}
}

// getTokenSymbol 获取代币符号（简化实现）
func (u *UniswapExtractor) getTokenSymbol(tokenAddr string) string {
	// 简化实现：返回地址的前6位作为符号
	// 实际应该通过合约调用获取真实的代币符号
	if len(tokenAddr) >= 6 {
		return strings.ToUpper(tokenAddr[2:8])
	}
	return "UNKNOWN"
}

// parseLiquidityAmounts 从流动性事件日志中解析金额
func (u *UniswapExtractor) parseLiquidityAmounts(log *types.Log) (amount0, amount1 *big.Int) {
	if len(log.Data) < 64 {
		return big.NewInt(0), big.NewInt(0)
	}

	// 解析流动性金额（Mint/Burn事件格式相似）
	amount0 = new(big.Int).SetBytes(log.Data[0:32])
	amount1 = new(big.Int).SetBytes(log.Data[32:64])

	return amount0, amount1
}

// enhanceCreateLiquidityFromLog 增强的流动性记录创建
func (u *UniswapExtractor) enhanceCreateLiquidityFromLog(log *types.Log, tx *core.UnifiedTransaction, side string) *model.Liquidity {
	poolAddr := log.Address.Hex()

	// 解析流动性金额
	amount0, amount1 := u.parseLiquidityAmounts(log)

	// 计算总价值（简化为两个金额的和）
	totalAmount := new(big.Int).Add(amount0, amount1)
	value := new(big.Float).SetInt(totalAmount)
	valueFloat64, _ := value.Float64()

	return &model.Liquidity{
		Addr:    poolAddr,
		Router:  tx.ToAddress,
		Factory: u.getFactoryAddress(log),
		Pool:    poolAddr,
		Hash:    tx.TxHash,
		From:    tx.FromAddress,
		Pos:     u.getPositionId(log),
		Side:    side,
		Amount:  totalAmount,
		Value:   valueFloat64,
		Time:    uint64(tx.Timestamp.Unix()),
		Key:     tx.TxHash + "_" + side,
		Extra: &model.LiquidityExtra{
			Key:     tx.TxHash + "_" + side,
			Amounts: amount1, // 第二个代币的金额
			Values:  u.bigIntsToFloat64s(amount0, amount1),
			Time:    uint64(tx.Timestamp.Unix()),
		},
	}
}

// getPositionId 获取流动性位置ID（主要用于V3）
func (u *UniswapExtractor) getPositionId(log *types.Log) string {
	// 对于V3，可以从topics中获取position ID
	// 简化实现：使用交易哈希作为位置ID
	if len(log.Topics) > 1 {
		return log.Topics[1].Hex()
	}
	return ""
}

// bigIntsToFloat64s 将big.Int数组转换为float64数组
func (u *UniswapExtractor) bigIntsToFloat64s(amounts ...*big.Int) []float64 {
	result := make([]float64, len(amounts))
	for i, amount := range amounts {
		if amount != nil {
			f, _ := new(big.Float).SetInt(amount).Float64()
			result[i] = f
		}
	}
	return result
}
