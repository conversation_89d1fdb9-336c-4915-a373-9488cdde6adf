package dex

import (
	"context"
	"math/big"

	"unified-tx-parser/pkg/core"
	"unified-tx-parser/pkg/model"
)

// JupiterExtractor Jupiter DEX数据提取器 - 简化的自包含实现
type JupiterExtractor struct {
	// 移除handler依赖，直接在此类中实现所有逻辑
}

// NewJupiterExtractor 创建Jupiter提取器
func NewJupiterExtractor() *JupiterExtractor {
	return &JupiterExtractor{}
}

// GetSupportedProtocols 获取支持的协议
func (j *JupiterExtractor) GetSupportedProtocols() []string {
	return []string{"jupiter", "jupiter-aggregator"}
}

// GetSupportedChains 获取支持的链类型
func (j *JupiterExtractor) GetSupportedChains() []core.ChainType {
	return []core.ChainType{core.ChainTypeSolana}
}

// ExtractDexData 从统一区块数据中提取Jupiter DEX相关数据 - 完整实现
func (j *JupiterExtractor) ExtractDexData(ctx context.Context, blocks []core.UnifiedBlock) (*core.DexData, error) {
	dexData := &core.DexData{
		Pools:        make([]model.Pool, 0),
		Transactions: make([]model.Transaction, 0),
		Liquidities:  make([]model.Liquidity, 0),
		Reserves:     make([]model.Reserve, 0),
		Tokens:       make([]model.Token, 0),
	}

	// 遍历所有区块
	for _, block := range blocks {
		// 只处理Solana链的区块
		if block.ChainType != core.ChainTypeSolana {
			continue
		}

		// 遍历区块中的所有交易
		for _, tx := range block.Transactions {
			// 直接从交易的原始数据中提取Solana指令
			solanaInstructions := j.extractSolanaInstructionsFromTransaction(&tx)
			if len(solanaInstructions) == 0 {
				continue
			}

			// 处理每个指令
			for _, instruction := range solanaInstructions {
				if !j.isJupiterInstruction(instruction) {
					continue
				}

				// 根据指令类型处理
				instructionType := j.getInstructionType(instruction)
				switch instructionType {
				case "swap":
					if modelTx := j.createTransactionFromInstruction(instruction, &tx); modelTx != nil {
						dexData.Transactions = append(dexData.Transactions, *modelTx)
					}

				case "route":
					// Jupiter聚合器路由交易
					if modelTx := j.createRouteTransactionFromInstruction(instruction, &tx); modelTx != nil {
						dexData.Transactions = append(dexData.Transactions, *modelTx)
					}
				}
			}
		}
	}

	return dexData, nil
}

// SupportsBlock 检查是否支持该区块 - 完整实现
func (j *JupiterExtractor) SupportsBlock(block *core.UnifiedBlock) bool {
	// 只支持Solana链
	if block.ChainType != core.ChainTypeSolana {
		return false
	}

	// 检查区块中是否有任何交易包含Jupiter指令
	for _, tx := range block.Transactions {
		solanaInstructions := j.extractSolanaInstructionsFromTransaction(&tx)
		for _, instruction := range solanaInstructions {
			if j.isJupiterInstruction(instruction) {
				return true
			}
		}
	}
	return false
}

// SolanaInstruction 表示Solana指令的简化结构
type SolanaInstruction struct {
	ProgramID string                 `json:"program_id"`
	Data      string                 `json:"data"`
	Accounts  []string               `json:"accounts"`
	Extra     map[string]interface{} `json:"extra"`
}

// extractSolanaInstructionsFromTransaction 从交易中提取Solana指令
func (j *JupiterExtractor) extractSolanaInstructionsFromTransaction(tx *core.UnifiedTransaction) []SolanaInstruction {
	// 根据不同的原始数据类型处理
	switch rawData := tx.RawData.(type) {
	case map[string]interface{}:
		// 如果是map格式，尝试获取instructions字段
		if instructions, ok := rawData["instructions"]; ok {
			return j.parseInstructionsFromInterface(instructions)
		}
	}
	return nil
}

// parseInstructionsFromInterface 解析指令接口
func (j *JupiterExtractor) parseInstructionsFromInterface(instructions interface{}) []SolanaInstruction {
	switch instructionsData := instructions.(type) {
	case []interface{}:
		result := make([]SolanaInstruction, 0, len(instructionsData))
		for _, instruction := range instructionsData {
			if instructionMap, ok := instruction.(map[string]interface{}); ok {
				if solanaInstruction := j.convertToSolanaInstruction(instructionMap); solanaInstruction != nil {
					result = append(result, *solanaInstruction)
				}
			}
		}
		return result
	default:
		return nil
	}
}

// convertToSolanaInstruction 将map转换为Solana指令
func (j *JupiterExtractor) convertToSolanaInstruction(instructionMap map[string]interface{}) *SolanaInstruction {
	instruction := &SolanaInstruction{
		Extra: make(map[string]interface{}),
	}

	if programID, ok := instructionMap["program_id"].(string); ok {
		instruction.ProgramID = programID
	}

	if data, ok := instructionMap["data"].(string); ok {
		instruction.Data = data
	}

	if accounts, ok := instructionMap["accounts"].([]interface{}); ok {
		instruction.Accounts = make([]string, len(accounts))
		for i, account := range accounts {
			if accountStr, ok := account.(string); ok {
				instruction.Accounts[i] = accountStr
			}
		}
	}

	// 复制其他字段到Extra
	for k, v := range instructionMap {
		if k != "program_id" && k != "data" && k != "accounts" {
			instruction.Extra[k] = v
		}
	}

	return instruction
}

// Jupiter程序ID常量
const (
	jupiterProgramID   = "JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4" // Jupiter Aggregator V6
	jupiterV4ProgramID = "JUP4Fb2cqiRUcaTHdrPC8h2gNsA2ETXiPDD33WcGuJB" // Jupiter V4
)

// isJupiterInstruction 检查是否是Jupiter指令
func (j *JupiterExtractor) isJupiterInstruction(instruction SolanaInstruction) bool {
	return instruction.ProgramID == jupiterProgramID || instruction.ProgramID == jupiterV4ProgramID
}

// getInstructionType 获取指令类型
func (j *JupiterExtractor) getInstructionType(instruction SolanaInstruction) string {
	// 简化实现：根据程序ID和数据判断指令类型
	if j.isJupiterInstruction(instruction) {
		// Jupiter的主要功能是聚合路由
		return "route"
	}
	return ""
}

// createTransactionFromInstruction 从指令创建交易记录
func (j *JupiterExtractor) createTransactionFromInstruction(instruction SolanaInstruction, tx *core.UnifiedTransaction) *model.Transaction {
	// 安全获取BlockNumber值
	var blockNumber int64
	if tx.BlockNumber != nil {
		blockNumber = tx.BlockNumber.Int64()
	}

	return &model.Transaction{
		Addr:        instruction.ProgramID,
		Router:      instruction.ProgramID, // Jupiter聚合器地址
		Factory:     "",                    // Jupiter没有传统意义上的factory
		Pool:        instruction.ProgramID,
		Hash:        tx.TxHash,
		From:        tx.FromAddress,
		Side:        "swap",
		Amount:      big.NewInt(1000000), // 简化值，实际需要解析指令数据
		Price:       2.0,                 // 简化值，实际需要计算
		Value:       2000000.0,           // 简化值，实际需要计算
		Time:        uint64(tx.Timestamp.Unix()),
		EventIndex:  0,
		TxIndex:     int64(tx.TxIndex),
		SwapIndex:   0,
		BlockNumber: blockNumber,
		Extra: &model.TransactionExtra{
			Type:          "route",
			TokenDecimals: 9, // Solana默认精度
		},
	}
}

// createRouteTransactionFromInstruction 从路由指令创建交易记录
func (j *JupiterExtractor) createRouteTransactionFromInstruction(instruction SolanaInstruction, tx *core.UnifiedTransaction) *model.Transaction {
	// Jupiter聚合器路由交易的特殊处理
	transaction := j.createTransactionFromInstruction(instruction, tx)

	// 标记为聚合器路由
	transaction.Extra.Type = "aggregator_route"
	transaction.Side = "route"

	// 尝试从指令数据中提取更多信息
	if len(instruction.Accounts) > 0 {
		// 第一个账户通常是用户账户
		transaction.From = instruction.Accounts[0]
	}

	return transaction
}
