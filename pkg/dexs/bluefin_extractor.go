package dex

import (
	"context"
	"encoding/json"
	"fmt"
	"math/big"
	"strings"

	"unified-tx-parser/pkg/chains/sui"
	"unified-tx-parser/pkg/core"
	"unified-tx-parser/pkg/model"

	"github.com/block-vision/sui-go-sdk/models"
)

// Bluefin协议常量
const (
	// Bluefin AMM合约地址
	bluefinAmmAddr = "0x3492c874c1e3b3e2984e8c41b589e642d4d0a5d6459e5a9cfc2d52fd7c89c267"

	// Bluefin事件类型
	bluefinPoolCreatedEventType     = bluefinAmmAddr + "::events::PoolCreated"
	bluefinAddLiquidityEventType    = bluefinAmmAddr + "::events::LiquidityProvided"
	bluefinRemoveLiquidityEventType = bluefinAmmAddr + "::events::LiquidityRemoved"
	bluefinAssetSwapEventType       = bluefinAmmAddr + "::events::AssetSwap"
	bluefinFlashSwapEventType       = bluefinAmmAddr + "::events::FlashSwap"
)

// BluefinExtractor Bluefin DEX数据提取器 - 简化的自包含实现
type BluefinExtractor struct {
	client *sui.SuiProcessor
	// 移除handler依赖，直接在此类中实现所有逻辑
}

// NewBluefinExtractor 创建Bluefin提取器
func NewBluefinExtractor() *BluefinExtractor {
	return &BluefinExtractor{}
}

// SetSuiProcessor 设置Sui处理器（用于获取链上数据）
func (b *BluefinExtractor) SetSuiProcessor(processor interface{}) {
	if suiProcessor, ok := processor.(*sui.SuiProcessor); ok {
		b.client = suiProcessor
	}
}

// GetSupportedProtocols 获取支持的协议
func (b *BluefinExtractor) GetSupportedProtocols() []string {
	return []string{"bluefin"}
}

// GetSupportedChains 获取支持的链类型
func (b *BluefinExtractor) GetSupportedChains() []core.ChainType {
	return []core.ChainType{core.ChainTypeSui}
}

// ExtractDexData 从统一区块数据中提取Bluefin DEX相关数据 - 简化实现
func (b *BluefinExtractor) ExtractDexData(ctx context.Context, blocks []core.UnifiedBlock) (*core.DexData, error) {
	// 检查是否设置了Sui处理器
	if b.client == nil {
		return nil, fmt.Errorf("Sui处理器未设置，请先调用SetSuiProcessor方法")
	}

	dexData := &core.DexData{
		Pools:        make([]model.Pool, 0),
		Transactions: make([]model.Transaction, 0),
		Liquidities:  make([]model.Liquidity, 0),
		Reserves:     make([]model.Reserve, 0),
		Tokens:       make([]model.Token, 0),
	}

	// 遍历所有区块
	for _, block := range blocks {
		// 只处理Sui链的区块
		if block.ChainType != core.ChainTypeSui {
			continue
		}

		// 遍历区块中的所有交易
		for _, tx := range block.Transactions {
			// 直接从交易的原始数据中提取Sui事件
			suiEvents := b.extractSuiEventsFromTransaction(&tx)
			if len(suiEvents) == 0 {
				continue
			}

			// 处理每个事件
			for _, event := range suiEvents {
				if !b.isBluefinEvent(event) {
					continue
				}

				// 根据事件类型处理
				eventType := b.getEventType(event)
				switch eventType {
				case "swap":
					swapData := b.processSwapEvent(ctx, event, &tx)
					if swapData != nil {
						dexData.Pools = append(dexData.Pools, swapData.Pool)
						dexData.Tokens = append(dexData.Tokens, swapData.Tokens...)
						dexData.Reserves = append(dexData.Reserves, swapData.Reserve)
						dexData.Transactions = append(dexData.Transactions, swapData.Transactions...)
					}

				case "add_liquidity":
					if liquidity := b.createLiquidityFromEvent(event, &tx, "add"); liquidity != nil {
						dexData.Liquidities = append(dexData.Liquidities, *liquidity)
					}

				case "remove_liquidity":
					if liquidity := b.createLiquidityFromEvent(event, &tx, "remove"); liquidity != nil {
						dexData.Liquidities = append(dexData.Liquidities, *liquidity)
					}

				case "pool_created":
					if pool := b.createPoolFromEvent(event, &tx); pool != nil {
						dexData.Pools = append(dexData.Pools, *pool)
					}
				}
			}
		}
	}

	return dexData, nil
}

const shortCoinType = "0x2::sui::SUI"

// SwapEventData 交换事件处理结果
type SwapEventData struct {
	Pool         model.Pool
	Tokens       []model.Token
	Reserve      model.Reserve
	Transactions []model.Transaction
}

// 获取pool池里面代币地址
func (b *BluefinExtractor) ExtractPoolCoin(coinType string) (string, string) {
	// 检查输入是否为空
	if coinType == "" {
		fmt.Printf("coinType is empty\n")
		return "", ""
	}

	start := strings.Index(coinType, "<")
	end := strings.Index(coinType, ">")

	// 检查是否找到了 < 和 > 字符
	if start == -1 || end == -1 || start >= end {
		fmt.Printf("Invalid coinType format: %s\n", coinType)
		return "", ""
	}

	substr := coinType[start+1 : end] // 注意这里应该是 start+1，跳过 < 字符
	substr = strings.ReplaceAll(substr, " ", "")
	coins := strings.Split(substr, ",")

	for i := 0; i < len(coins); i++ {
		if strings.EqualFold(coins[i], shortCoinType) {
			coins[i] = shortCoinType
		}
	}

	if len(coins) >= 2 {
		return coins[0], coins[1]
	}

	fmt.Printf("Not enough coins found in coinType: %s, coins: %v\n", coinType, coins)
	return "", ""
}

// processSwapEvent 处理交换事件
func (b *BluefinExtractor) processSwapEvent(ctx context.Context, event map[string]interface{}, tx *core.UnifiedTransaction) *SwapEventData {
	// 提取pool_id
	poolId := b.extractPoolIdFromEvent(event)
	if poolId == "" {
		return nil
	}

	// 获取池对象
	poolObject, err := b.getPoolObject(ctx, poolId)
	if err != nil {
		fmt.Printf("Failed to get pool object for %s: %v\n", poolId, err)
		return nil
	}

	// 提取token地址
	token0, token1 := b.ExtractPoolCoin(poolObject.Data.Type)
	if token0 == "" || token1 == "" {
		fmt.Printf("Failed to extract token addresses from pool type: %s\n", poolObject.Data.Type)
		return nil
	}

	// 获取token储备量
	token0Reserve, token1Reserve, _ := b.getPoolTokenBalances(token0, token1, poolObject)

	// 获取token元数据
	tokens, err := b.getTokensMetadata(ctx, token0, token1)
	if err != nil {
		fmt.Printf("Failed to get tokens metadata: %v\n", err)
		return nil
	}

	// 构建返回数据
	return &SwapEventData{
		Pool: model.Pool{
			Addr:     poolId,
			Factory:  bluefinAmmAddr,
			Protocol: "bluefin",
			Tokens: map[int]string{
				0: token0,
				1: token1,
			},
		},
		Tokens: tokens,
		Reserve: model.Reserve{
			Addr: poolId,
			Amounts: map[int]*big.Int{
				0: token0Reserve,
				1: token1Reserve,
			},
			Time: uint64(tx.Timestamp.Unix()),
		},
		Transactions: []model.Transaction{
			{
				Addr:    token0,
				Factory: bluefinAmmAddr,
				Pool:    poolId,
				Hash:    tx.TxHash,
				From:    tx.FromAddress,
				Time:    uint64(tx.Timestamp.Unix()),
			},
			{
				Addr:    token1,
				Factory: bluefinAmmAddr,
				Pool:    poolId,
				Hash:    tx.TxHash,
				From:    tx.FromAddress,
				Time:    uint64(tx.Timestamp.Unix()),
			},
		},
	}
}

// token0Reserve token1Reserve
func (b *BluefinExtractor) getPoolTokenBalances(token0, token1 string, poolObject models.SuiObjectResponse) (*big.Int, *big.Int, error) {
	balanceCoinA := poolObject.Data.Content.Fields["coin_a"].(string)
	balanceCoinB := poolObject.Data.Content.Fields["coin_b"].(string)
	if balanceCoinA == "" || balanceCoinB == "" {
		return &big.Int{}, &big.Int{}, fmt.Errorf("无法获取池子代币余额")
	}
	balanceCoinAValue, ok := new(big.Int).SetString(balanceCoinA, 10)
	if !ok {
		return &big.Int{}, &big.Int{}, fmt.Errorf("无法解析池子代币余额")
	}
	balanceCoinBValue, ok := new(big.Int).SetString(balanceCoinB, 10)
	if !ok {
		return &big.Int{}, &big.Int{}, fmt.Errorf("无法解析池子代币余额")
	}
	return balanceCoinAValue, balanceCoinBValue, nil
}

// SupportsBlock 检查是否支持该区块 - 简化实现
func (b *BluefinExtractor) SupportsBlock(block *core.UnifiedBlock) bool {
	// 只支持Sui链
	if block.ChainType != core.ChainTypeSui {
		return false
	}

	// 检查区块中是否有任何交易包含Bluefin事件
	for _, tx := range block.Transactions {
		suiEvents := b.extractSuiEventsFromTransaction(&tx)
		for _, event := range suiEvents {
			if b.isBluefinEvent(event) {
				return true
			}
		}
	}
	return false
}

// extractSuiEventsFromTransaction 从交易中提取Sui事件
func (b *BluefinExtractor) extractSuiEventsFromTransaction(tx *core.UnifiedTransaction) []map[string]interface{} {
	// 根据不同的原始数据类型处理
	switch rawData := tx.RawData.(type) {
	case map[string]interface{}:
		// 如果是map格式，尝试获取events字段
		if events, ok := rawData["events"]; ok {
			return b.parseEventsFromInterface(events)
		}
	default:
		// 尝试通过JSON解析
		data, err := json.Marshal(rawData)
		if err != nil {
			return nil
		}

		var result map[string]interface{}
		if err := json.Unmarshal(data, &result); err != nil {
			return nil
		}

		if events, ok := result["events"]; ok {
			return b.parseEventsFromInterface(events)
		}
	}

	return nil
}

// parseEventsFromInterface 解析事件接口
func (b *BluefinExtractor) parseEventsFromInterface(events interface{}) []map[string]interface{} {
	switch eventsData := events.(type) {
	case []interface{}:
		result := make([]map[string]interface{}, 0, len(eventsData))
		for _, event := range eventsData {
			if eventMap, ok := event.(map[string]interface{}); ok {
				result = append(result, eventMap)
			}
		}
		return result
	case []map[string]interface{}:
		return eventsData
	default:
		return nil
	}
}

// isBluefinEvent 检查是否是Bluefin事件
func (b *BluefinExtractor) isBluefinEvent(event map[string]interface{}) bool {
	eventType, ok := event["type"].(string)
	if !ok {
		return false
	}

	// 检查是否包含Bluefin合约地址
	return eventType == bluefinPoolCreatedEventType ||
		eventType == bluefinAddLiquidityEventType ||
		eventType == bluefinRemoveLiquidityEventType ||
		eventType == bluefinAssetSwapEventType ||
		eventType == bluefinFlashSwapEventType
}

// getEventType 获取事件类型
func (b *BluefinExtractor) getEventType(event map[string]interface{}) string {
	eventType, ok := event["type"].(string)
	if !ok {
		return ""
	}

	switch eventType {
	case bluefinAssetSwapEventType, bluefinFlashSwapEventType:
		return "swap"
	case bluefinAddLiquidityEventType:
		return "add_liquidity"
	case bluefinRemoveLiquidityEventType:
		return "remove_liquidity"
	case bluefinPoolCreatedEventType:
		return "pool_created"
	default:
		return ""
	}
}

// createTransactionFromEvent 从事件创建交易记录
func (b *BluefinExtractor) createTransactionFromEvent(event map[string]interface{}, tx *core.UnifiedTransaction) *model.Transaction {
	// 从事件中提取交易相关信息
	parsedFields := event["parsedJson"]
	if parsedFields == nil {
		return nil
	}

	fields, ok := parsedFields.(map[string]interface{})
	if !ok {
		return nil
	}

	// 提取基本信息
	poolAddr := b.getStringField(fields, "pool_id")
	if poolAddr == "" {
		return nil
	}

	// 提取金额和价格信息
	amountIn := b.getBigIntField(fields, "amount_in")
	amountOut := b.getBigIntField(fields, "amount_out")

	var price float64
	if amountIn != nil && amountOut != nil && amountIn.Cmp(big.NewInt(0)) > 0 {
		price = float64(amountOut.Int64()) / float64(amountIn.Int64())
	}

	// 安全获取BlockNumber值
	var blockNumber int64
	if tx.BlockNumber != nil {
		blockNumber = tx.BlockNumber.Int64()
	}

	return &model.Transaction{
		Addr:        poolAddr,
		Router:      tx.ToAddress,
		Factory:     "",
		Pool:        poolAddr,
		Hash:        tx.TxHash,
		From:        tx.FromAddress,
		Side:        "swap",
		Amount:      amountIn,
		Price:       price,
		Value:       price * float64(amountIn.Int64()),
		Time:        uint64(tx.Timestamp.Unix()),
		EventIndex:  0,
		TxIndex:     int64(tx.TxIndex),
		SwapIndex:   0,
		BlockNumber: blockNumber,
		Extra: &model.TransactionExtra{
			QuoteAddr:     b.getStringField(fields, "token_out"),
			QuotePrice:    fmt.Sprintf("%.6f", price),
			Type:          "swap",
			TokenSymbol:   "",
			TokenDecimals: 9,
		},
	}
}

// createLiquidityFromEvent 从事件创建流动性记录
func (b *BluefinExtractor) createLiquidityFromEvent(event map[string]interface{}, tx *core.UnifiedTransaction, side string) *model.Liquidity {
	parsedFields := event["parsedJson"]
	if parsedFields == nil {
		return nil
	}

	fields, ok := parsedFields.(map[string]interface{})
	if !ok {
		return nil
	}

	poolAddr := b.getStringField(fields, "pool_id")
	if poolAddr == "" {
		return nil
	}

	amount := b.getBigIntField(fields, "amount")
	if amount == nil {
		amount = big.NewInt(0)
	}

	return &model.Liquidity{
		Addr:    poolAddr,
		Router:  tx.ToAddress,
		Factory: "",
		Pool:    poolAddr,
		Hash:    tx.TxHash,
		From:    tx.FromAddress,
		Pos:     "",
		Side:    side,
		Amount:  amount,
		Value:   float64(amount.Int64()),
		Time:    uint64(tx.Timestamp.Unix()),
		Key:     tx.TxHash + "_" + side,
		Extra: &model.LiquidityExtra{
			Key:    tx.TxHash + "_" + side,
			Values: []float64{float64(amount.Int64())},
			Time:   uint64(tx.Timestamp.Unix()),
		},
	}
}

// createPoolFromEvent 从事件创建池子记录
func (b *BluefinExtractor) createPoolFromEvent(event map[string]interface{}, tx *core.UnifiedTransaction) *model.Pool {
	parsedFields := event["parsedJson"]
	if parsedFields == nil {
		return nil
	}

	fields, ok := parsedFields.(map[string]interface{})
	if !ok {
		return nil
	}

	poolAddr := b.getStringField(fields, "pool_id")
	if poolAddr == "" {
		return nil
	}

	return &model.Pool{
		Addr:     poolAddr,
		Factory:  bluefinAmmAddr,
		Protocol: "bluefin",
		Tokens:   make(map[int]string),
		Fee:      0,
		Extra: &model.PoolExtra{
			Hash: tx.TxHash,
			From: tx.FromAddress,
			Time: uint64(tx.Timestamp.Unix()),
		},
	}
}

// getStringField 安全获取字符串字段
func (b *BluefinExtractor) getStringField(fields map[string]interface{}, key string) string {
	if value, ok := fields[key]; ok {
		if str, ok := value.(string); ok {
			return str
		}
	}
	return ""
}

// getBigIntField 安全获取大整数字段
func (b *BluefinExtractor) getBigIntField(fields map[string]interface{}, key string) *big.Int {
	if value, ok := fields[key]; ok {
		switch v := value.(type) {
		case string:
			if result, ok := new(big.Int).SetString(v, 10); ok {
				return result
			}
		case int64:
			return big.NewInt(v)
		case float64:
			return big.NewInt(int64(v))
		}
	}
	return nil
}

// extractPoolIdFromEvent 从事件中提取pool_id
func (b *BluefinExtractor) extractPoolIdFromEvent(event map[string]interface{}) string {
	parsedJson := event["parsedJson"]
	if parsedJson == nil {
		fmt.Println("parsedJson is nil")
		return ""
	}

	fields, ok := parsedJson.(map[string]interface{})
	if !ok {
		fmt.Println("parsedJson is not a map")
		return ""
	}

	poolId, ok := fields["pool_id"].(string)
	if !ok {
		fmt.Println("pool_id not found or not a string:", fields["pool_id"])
		return ""
	}

	//fmt.Println("Found poolId:", poolId)
	return poolId
}

// getPoolObject 获取池对象
func (b *BluefinExtractor) getPoolObject(ctx context.Context, poolId string) (models.SuiObjectResponse, error) {
	return b.client.GetObject(ctx, models.SuiGetObjectRequest{
		ObjectId: poolId,
		Options: models.SuiObjectDataOptions{
			ShowType:                true,
			ShowContent:             true,
			ShowBcs:                 false,
			ShowOwner:               false,
			ShowPreviousTransaction: false,
			ShowStorageRebate:       false,
			ShowDisplay:             false,
		},
	})
}

// getTokensMetadata 获取token元数据
func (b *BluefinExtractor) getTokensMetadata(ctx context.Context, token0, token1 string) ([]model.Token, error) {
	token0Metadata, err := b.client.GetToken(ctx, models.SuiXGetCoinMetadataRequest{
		CoinType: token0,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get token0 metadata: %w", err)
	}

	token1Metadata, err := b.client.GetToken(ctx, models.SuiXGetCoinMetadataRequest{
		CoinType: token1,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get token1 metadata: %w", err)
	}

	return []model.Token{
		{
			Addr:     token0,
			Symbol:   token0Metadata.Symbol,
			Decimals: token0Metadata.Decimals,
			Name:     token0Metadata.Name,
		},
		{
			Addr:     token1,
			Symbol:   token1Metadata.Symbol,
			Decimals: token1Metadata.Decimals,
			Name:     token1Metadata.Name,
		},
	}, nil
}
