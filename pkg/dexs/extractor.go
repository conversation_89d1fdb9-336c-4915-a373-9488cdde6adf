package dex

import (
	"context"
	"log"
	"strconv"
	"strings"

	"unified-tx-parser/pkg/core"
	"unified-tx-parser/pkg/model"
	"unified-tx-parser/pkg/utils"
)

// DEXExtractor 通用DEX事件提取器 (兼容性包装器)
// 新架构中每个DEX都有自己的提取器实现
type DEXExtractor struct {
	// 支持的协议处理器 (保留用于向后兼容)
	protocolHandlers map[string]ProtocolHandler

	// 支持的链类型
	supportedChains []core.ChainType

	// 新的提取器工厂
	factory *ExtractorFactory
}

// ProtocolHandler 协议处理器接口
type ProtocolHandler interface {
	// 获取协议名称
	GetProtocolName() string

	// 检查是否支持该交易
	SupportsTransaction(tx *core.UnifiedTransaction) bool

	// 提取事件
	ExtractEvents(ctx context.Context, tx *core.UnifiedTransaction) ([]core.BusinessEvent, error)

	// 获取支持的链类型
	GetSupportedChains() []core.ChainType
}

// NewDEXExtractor 创建DEX提取器
func NewDEXExtractor() *DEXExtractor {
	extractor := &DEXExtractor{
		protocolHandlers: make(map[string]ProtocolHandler),
		supportedChains: []core.ChainType{
			core.ChainTypeEthereum,
			core.ChainTypeSolana,
			core.ChainTypeSui,
		},
	}

	// 注册内置协议处理器
	extractor.registerBuiltinHandlers()

	return extractor
}

// NewDEXExtractorWithConfig 根据配置创建DEX事件提取器
func NewDEXExtractorWithConfig(protocols map[string]interface{}) *DEXExtractor {
	extractor := &DEXExtractor{
		protocolHandlers: make(map[string]ProtocolHandler),
		supportedChains:  make([]core.ChainType, 0),
		factory:          CreateFactoryWithConfig(protocols),
	}

	// 根据配置选择性注册协议处理器
	extractor.registerConfiguredHandlers(protocols)

	return extractor
}

// registerBuiltinHandlers 注册内置协议处理器
func (d *DEXExtractor) registerBuiltinHandlers() {
	// 注册Uniswap处理器 (以太坊/BSC) - 已移除，现在使用UniswapExtractor

	// 注册Bluefin处理器 (Sui) - 已移除，现在使用BluefinExtractor

	// 注册Jupiter处理器 (Solana) - 已移除，现在使用JupiterExtractor

	// 注册PancakeSwap处理器 (BSC) - 已移除，现在使用PancakeSwapExtractor

	// 统一输出注册结果，显示链-DEX映射
	chainDexMap := make(map[string][]string)
	for name, handler := range d.protocolHandlers {
		chains := handler.GetSupportedChains()
		for _, chain := range chains {
			chainStr := strings.ToUpper(string(chain))
			chainDexMap[chainStr] = append(chainDexMap[chainStr], name)
		}
	}

	log.Printf("📦 协议映射:")
	for chain, dexs := range chainDexMap {
		log.Printf("   [%s] %v", chain, dexs)
	}
}

// registerConfiguredHandlers 根据配置注册协议处理器
func (d *DEXExtractor) registerConfiguredHandlers(protocols map[string]interface{}) {
	registeredProtocols := make([]string, 0)

	// 检查每个协议是否在配置中启用
	for protocolName := range protocols {
		var handler ProtocolHandler

		switch strings.ToLower(protocolName) {
		case "uniswap":
			// Uniswap现在使用独立的提取器，不再需要handler
			continue
		case "bluefin":
			// Bluefin现在使用独立的提取器，不再需要handler
			continue
		case "jupiter":
			// Jupiter现在使用独立的提取器，不再需要handler
			continue
		case "pancakeswap":
			// PancakeSwap现在使用独立的提取器，不再需要handler
			continue
		default:
			continue // 跳过未知协议
		}

		if handler != nil {
			d.registerProtocolHandlerSilent(handler)
			registeredProtocols = append(registeredProtocols, protocolName)
		}
	}

	// 只输出已注册的协议
	if len(registeredProtocols) > 0 {
		log.Printf("📦 已注册协议: %v", registeredProtocols)
	}
}

// RegisterProtocolHandler 注册协议处理器（带日志）
func (d *DEXExtractor) RegisterProtocolHandler(handler ProtocolHandler) {
	d.registerProtocolHandlerSilent(handler)
	protocolName := handler.GetProtocolName()
	handlerChains := handler.GetSupportedChains()
	log.Printf("📦 注册DEX协议处理器: %s (支持链: %v)", protocolName, handlerChains)
}

// registerProtocolHandlerSilent 静默注册协议处理器（无日志）
func (d *DEXExtractor) registerProtocolHandlerSilent(handler ProtocolHandler) {
	protocolName := handler.GetProtocolName()
	d.protocolHandlers[protocolName] = handler

	// 更新支持的链类型
	handlerChains := handler.GetSupportedChains()
	for _, chain := range handlerChains {
		// 检查是否已存在
		found := false
		for _, existingChain := range d.supportedChains {
			if existingChain == chain {
				found = true
				break
			}
		}
		if !found {
			d.supportedChains = append(d.supportedChains, chain)
		}
	}
}

// GetSupportedProtocols 获取支持的协议
func (d *DEXExtractor) GetSupportedProtocols() []string {
	protocols := make([]string, 0, len(d.protocolHandlers))
	for protocol := range d.protocolHandlers {
		protocols = append(protocols, protocol)
	}
	return protocols
}

// GetSupportedChains 获取支持的链类型
func (d *DEXExtractor) GetSupportedChains() []core.ChainType {
	return d.supportedChains
}

// ExtractEvents 从交易中提取业务事件
func (d *DEXExtractor) ExtractEvents(ctx context.Context, tx *core.UnifiedTransaction) ([]core.BusinessEvent, error) {
	var allEvents []core.BusinessEvent

	// 遍历所有协议处理器
	for protocolName, handler := range d.protocolHandlers {
		// 检查是否支持该交易
		if !handler.SupportsTransaction(tx) {
			continue
		}

		// 提取事件
		events, err := handler.ExtractEvents(ctx, tx)
		if err != nil {
			log.Printf("⚠️ %s-%s: 提取失败 - %v", tx.ChainType, protocolName, err)
			continue
		}

		if len(events) > 0 {
			// 简化日志：只显示关键信息，不每个事件都打印
			allEvents = append(allEvents, events...)
		}
	}

	return allEvents, nil
}

// ExtractDexData 从统一区块数据中提取DEX相关数据 (新接口方法)
func (d *DEXExtractor) ExtractDexData(ctx context.Context, blocks []core.UnifiedBlock) (*core.DexData, error) {
	// 使用新的工厂模式提取数据
	if d.factory != nil {
		return d.extractWithFactory(ctx, blocks)
	}

	// 向后兼容：使用旧的方法
	return d.extractWithLegacyHandlers(ctx, blocks)
}

// extractWithFactory 使用工厂模式提取数据
func (d *DEXExtractor) extractWithFactory(ctx context.Context, blocks []core.UnifiedBlock) (*core.DexData, error) {
	// 创建空的DEX数据结构
	dexData := &core.DexData{
		Pools:        make([]model.Pool, 0),
		Transactions: make([]model.Transaction, 0),
		Liquidities:  make([]model.Liquidity, 0),
		Reserves:     make([]model.Reserve, 0),
		Tokens:       make([]model.Token, 0),
	}

	// 获取所有提取器
	extractors := d.factory.GetAllExtractors()

	// 使用每个提取器处理数据
	for _, extractor := range extractors {
		// 过滤支持的区块
		supportedBlocks := make([]core.UnifiedBlock, 0)
		for _, block := range blocks {
			if extractor.SupportsBlock(&block) {
				supportedBlocks = append(supportedBlocks, block)
			}
		}

		if len(supportedBlocks) == 0 {
			continue
		}

		// 提取DEX数据
		extractorData, err := extractor.ExtractDexData(ctx, supportedBlocks)
		if err != nil {
			log.Printf("⚠️ DEX提取器提取数据失败: %v", err)
			continue
		}

		// 合并数据
		dexData.Pools = append(dexData.Pools, extractorData.Pools...)
		dexData.Transactions = append(dexData.Transactions, extractorData.Transactions...)
		dexData.Liquidities = append(dexData.Liquidities, extractorData.Liquidities...)
		dexData.Reserves = append(dexData.Reserves, extractorData.Reserves...)
		dexData.Tokens = append(dexData.Tokens, extractorData.Tokens...)
	}

	return dexData, nil
}

// extractWithLegacyHandlers 使用旧的处理器提取数据 (向后兼容)
func (d *DEXExtractor) extractWithLegacyHandlers(ctx context.Context, blocks []core.UnifiedBlock) (*core.DexData, error) {
	// 创建空的DEX数据结构
	dexData := &core.DexData{
		Pools:        make([]model.Pool, 0),
		Transactions: make([]model.Transaction, 0),
		Liquidities:  make([]model.Liquidity, 0),
		Reserves:     make([]model.Reserve, 0),
		Tokens:       make([]model.Token, 0),
	}

	// 遍历所有区块
	for _, block := range blocks {
		// 遍历区块中的所有交易
		for _, tx := range block.Transactions {
			// 提取业务事件
			events, err := d.ExtractEvents(ctx, &tx)
			if err != nil {
				log.Printf("⚠️ 提取事件失败 (tx: %s): %v", tx.TxHash, err)
				continue
			}

			// 将业务事件转换为model数据结构
			// 这里需要根据具体的业务逻辑来实现转换
			// 暂时作为占位符实现
			for _, event := range events {
				switch event.EventType {
				case core.EventTypeSwap:
					// 转换为model.Transaction
					if swapData, ok := event.Data.(*core.SwapEventData); ok {
						// 解析价格字符串
						price := 0.0
						if priceFloat, err := strconv.ParseFloat(swapData.Price, 64); err == nil {
							price = priceFloat
						}

						modelTx := model.Transaction{
							Addr:        swapData.PoolID,
							Hash:        tx.TxHash,
							From:        tx.FromAddress,
							Side:        "swap",
							Amount:      swapData.AmountIn,
							Price:       price,
							Value:       price * float64(swapData.AmountIn.Int64()),
							Time:        uint64(tx.Timestamp.Unix()),
							EventIndex:  0,
							TxIndex:     int64(tx.TxIndex),
							BlockNumber: tx.BlockNumber.Int64(),
						}
						dexData.Transactions = append(dexData.Transactions, modelTx)
					}
				case core.EventTypeAddLiquidity:
					// 转换为model.Liquidity
					if liquidityData, ok := event.Data.(*core.LiquidityEventData); ok {
						modelLiquidity := model.Liquidity{
							Addr:   liquidityData.PoolID,
							Hash:   tx.TxHash,
							From:   liquidityData.Provider,
							Pool:   liquidityData.PoolID,
							Side:   "add",
							Amount: liquidityData.AmountA,
							Value:  float64(liquidityData.AmountA.Int64()),
							Time:   uint64(tx.Timestamp.Unix()),
						}
						dexData.Liquidities = append(dexData.Liquidities, modelLiquidity)
					}
				}
			}
		}
	}

	return dexData, nil
}

// SupportsBlock 检查是否支持该区块 (新接口方法)
func (d *DEXExtractor) SupportsBlock(block *core.UnifiedBlock) bool {
	// 检查区块中是否有任何交易被支持
	for _, tx := range block.Transactions {
		if d.SupportsTransaction(&tx) {
			return true
		}
	}
	return false
}

// SupportsTransaction 检查是否支持该交易
func (d *DEXExtractor) SupportsTransaction(tx *core.UnifiedTransaction) bool {
	// 检查链类型是否支持
	chainSupported := false
	for _, chain := range d.supportedChains {
		if chain == tx.ChainType {
			chainSupported = true
			break
		}
	}

	if !chainSupported {
		return false
	}

	// 检查是否有任何协议处理器支持该交易
	for _, handler := range d.protocolHandlers {
		if handler.SupportsTransaction(tx) {
			return true
		}
	}

	return false
}

// BaseProtocolHandler 基础协议处理器
type BaseProtocolHandler struct {
	protocolName    string
	supportedChains []core.ChainType
}

// GetProtocolName 获取协议名称
func (b *BaseProtocolHandler) GetProtocolName() string {
	return b.protocolName
}

// GetSupportedChains 获取支持的链类型
func (b *BaseProtocolHandler) GetSupportedChains() []core.ChainType {
	return b.supportedChains
}

// generateEventID 生成事件ID
func (b *BaseProtocolHandler) generateEventID(tx *core.UnifiedTransaction, eventIndex int) string {
	return utils.GenerateEventID(string(tx.ChainType), tx.TxHash, eventIndex)
}

// createBusinessEvent 创建业务事件
func (b *BaseProtocolHandler) createBusinessEvent(tx *core.UnifiedTransaction, eventType core.BusinessEventType, data interface{}, eventIndex int) core.BusinessEvent {
	return core.BusinessEvent{
		EventID:   b.generateEventID(tx, eventIndex),
		EventType: eventType,
		Protocol:  b.protocolName,
		TxHash:    tx.TxHash,
		ChainType: tx.ChainType,
		Data:      data,
		Timestamp: tx.Timestamp,
	}
}

// isContractAddress 检查是否是合约地址
func (b *BaseProtocolHandler) isContractAddress(address string, knownAddresses []string) bool {
	normalizedAddr := utils.NormalizeAddress(address)

	for _, knownAddr := range knownAddresses {
		if strings.EqualFold(normalizedAddr, utils.NormalizeAddress(knownAddr)) {
			return true
		}
	}

	return false
}
