package core

import (
	"context"
	"math/big"
	"testing"
	"time"
	"unified-tx-parser/pkg/model"
)

// TestUnifiedBlockStructure 测试统一区块结构
func TestUnifiedBlockStructure(t *testing.T) {
	// 创建测试区块
	block := UnifiedBlock{
		BlockNumber: big.NewInt(12345),
		BlockHash:   "0x1234567890abcdef",
		ChainType:   ChainTypeEthereum,
		ChainID:     "1",
		ParentHash:  "0x0987654321fedcba",
		Timestamp:   time.Now(),
		TxCount:     2,
		Transactions: []UnifiedTransaction{
			{
				TxHash:      "0xabc123",
				ChainType:   ChainTypeEthereum,
				ChainID:     "1",
				BlockNumber: big.NewInt(12345),
				FromAddress: "0x1111",
				ToAddress:   "0x2222",
				Value:       big.NewInt(1000),
				Timestamp:   time.Now(),
			},
		},
		Events: []UnifiedEvent{
			{
				EventID:     "event_1",
				ChainType:   ChainTypeEthereum,
				ChainID:     "1",
				BlockNumber: big.NewInt(12345),
				TxHash:      "0xabc123",
				EventType:   "Transfer",
				Address:     "0x3333",
				Timestamp:   time.Now(),
			},
		},
	}

	// 验证基本字段
	if block.BlockNumber.Cmp(big.NewInt(12345)) != 0 {
		t.Errorf("Expected block number 12345, got %s", block.BlockNumber.String())
	}

	if block.ChainType != ChainTypeEthereum {
		t.Errorf("Expected chain type ethereum, got %s", block.ChainType)
	}

	if len(block.Transactions) != 1 {
		t.Errorf("Expected 1 transaction, got %d", len(block.Transactions))
	}

	if len(block.Events) != 1 {
		t.Errorf("Expected 1 event, got %d", len(block.Events))
	}
}

// TestDexDataStructure 测试DEX数据结构
func TestDexDataStructure(t *testing.T) {
	// 创建测试DEX数据
	dexData := DexData{
		Pools: []model.Pool{
			{
				Addr:     "0xpool1",
				Protocol: "uniswap",
				Factory:  "0xfactory1",
				Tokens:   map[int]string{0: "0xtoken1", 1: "0xtoken2"},
				Fee:      3000,
			},
		},
		Transactions: []model.Transaction{
			{
				Addr:   "0xpool1",
				Hash:   "0xtx1",
				From:   "0xuser1",
				Side:   "buy",
				Amount: big.NewInt(1000),
				Price:  1.5,
				Value:  1500.0,
				Time:   uint64(time.Now().Unix()),
			},
		},
		Liquidities: []model.Liquidity{
			{
				Addr:   "0xpool1",
				Hash:   "0xtx2",
				From:   "0xuser2",
				Side:   "add",
				Amount: big.NewInt(2000),
				Value:  2000.0,
				Time:   uint64(time.Now().Unix()),
			},
		},
		Reserves: []model.Reserve{
			{
				Addr:    "0xpool1",
				Amounts: map[int]*big.Int{0: big.NewInt(10000), 1: big.NewInt(20000)},
				Time:    uint64(time.Now().Unix()),
			},
		},
		Tokens: []model.Token{
			{
				Addr:     "0xtoken1",
				Name:     "Token1",
				Symbol:   "TK1",
				Decimals: 18,
				IsStable: false,
			},
		},
	}

	// 验证基本字段
	if len(dexData.Pools) != 1 {
		t.Errorf("Expected 1 pool, got %d", len(dexData.Pools))
	}

	if len(dexData.Transactions) != 1 {
		t.Errorf("Expected 1 transaction, got %d", len(dexData.Transactions))
	}

	if len(dexData.Liquidities) != 1 {
		t.Errorf("Expected 1 liquidity, got %d", len(dexData.Liquidities))
	}

	if len(dexData.Reserves) != 1 {
		t.Errorf("Expected 1 reserve, got %d", len(dexData.Reserves))
	}

	if len(dexData.Tokens) != 1 {
		t.Errorf("Expected 1 token, got %d", len(dexData.Tokens))
	}

	// 验证具体数据
	pool := dexData.Pools[0]
	if pool.Protocol != "uniswap" {
		t.Errorf("Expected protocol uniswap, got %s", pool.Protocol)
	}

	if pool.Fee != 3000 {
		t.Errorf("Expected fee 3000, got %d", pool.Fee)
	}

	tx := dexData.Transactions[0]
	if tx.Side != "buy" {
		t.Errorf("Expected side buy, got %s", tx.Side)
	}

	if tx.Price != 1.5 {
		t.Errorf("Expected price 1.5, got %f", tx.Price)
	}

	token := dexData.Tokens[0]
	if token.Symbol != "TK1" {
		t.Errorf("Expected symbol TK1, got %s", token.Symbol)
	}

	if token.Decimals != 18 {
		t.Errorf("Expected decimals 18, got %d", token.Decimals)
	}
}

// MockChainProcessor 模拟链处理器用于测试
type MockChainProcessor struct {
	chainType ChainType
	chainID   string
}

func (m *MockChainProcessor) GetChainType() ChainType {
	return m.chainType
}

func (m *MockChainProcessor) GetChainID() string {
	return m.chainID
}

func (m *MockChainProcessor) GetLatestBlockNumber(ctx context.Context) (*big.Int, error) {
	return big.NewInt(12345), nil
}

func (m *MockChainProcessor) GetBlocksByRange(ctx context.Context, startBlock, endBlock *big.Int) ([]UnifiedBlock, error) {
	// 创建模拟交易
	mockTx := UnifiedTransaction{
		TxHash:      "0xabc123def456",
		ChainType:   m.chainType,
		ChainID:     m.chainID,
		BlockNumber: big.NewInt(12345),
		FromAddress: "0x1111111111111111111111111111111111111111",
		ToAddress:   "0x2222222222222222222222222222222222222222",
		Value:       big.NewInt(1000),
		TxIndex:     0,
		Timestamp:   time.Now(),
	}

	return []UnifiedBlock{
		{
			BlockNumber:  big.NewInt(12345),
			BlockHash:    "0x1234567890abcdef",
			ChainType:    m.chainType,
			ChainID:      m.chainID,
			TxCount:      1,
			Transactions: []UnifiedTransaction{mockTx},
			Events:       []UnifiedEvent{},
			Timestamp:    time.Now(),
		},
	}, nil
}

func (m *MockChainProcessor) GetBlock(ctx context.Context, blockNumber *big.Int) (*UnifiedBlock, error) {
	return &UnifiedBlock{
		BlockNumber:  blockNumber,
		BlockHash:    "0x1234567890abcdef",
		ChainType:    m.chainType,
		ChainID:      m.chainID,
		TxCount:      1,
		Transactions: []UnifiedTransaction{},
		Events:       []UnifiedEvent{},
	}, nil
}

func (m *MockChainProcessor) GetTransaction(ctx context.Context, txHash string) (*UnifiedTransaction, error) {
	return &UnifiedTransaction{
		TxHash:    txHash,
		ChainType: m.chainType,
		ChainID:   m.chainID,
	}, nil
}

func (m *MockChainProcessor) HealthCheck(ctx context.Context) error {
	return nil
}

// TestChainProcessorInterface 测试链处理器接口
func TestChainProcessorInterface(t *testing.T) {
	processor := &MockChainProcessor{
		chainType: ChainTypeEthereum,
		chainID:   "1",
	}

	ctx := context.Background()

	// 测试基本方法
	if processor.GetChainType() != ChainTypeEthereum {
		t.Errorf("Expected chain type ethereum, got %s", processor.GetChainType())
	}

	if processor.GetChainID() != "1" {
		t.Errorf("Expected chain ID 1, got %s", processor.GetChainID())
	}

	// 测试获取最新区块号
	latestBlock, err := processor.GetLatestBlockNumber(ctx)
	if err != nil {
		t.Errorf("GetLatestBlockNumber failed: %v", err)
	}
	if latestBlock.Cmp(big.NewInt(12345)) != 0 {
		t.Errorf("Expected latest block 12345, got %s", latestBlock.String())
	}

	// 测试获取区块范围
	blocks, err := processor.GetBlocksByRange(ctx, big.NewInt(12345), big.NewInt(12345))
	if err != nil {
		t.Errorf("GetBlocksByRange failed: %v", err)
	}
	if len(blocks) != 1 {
		t.Errorf("Expected 1 block, got %d", len(blocks))
	}

	// 测试获取单个区块
	block, err := processor.GetBlock(ctx, big.NewInt(12345))
	if err != nil {
		t.Errorf("GetBlock failed: %v", err)
	}
	if block.BlockNumber.Cmp(big.NewInt(12345)) != 0 {
		t.Errorf("Expected block number 12345, got %s", block.BlockNumber.String())
	}

	// 测试健康检查
	if err := processor.HealthCheck(ctx); err != nil {
		t.Errorf("HealthCheck failed: %v", err)
	}
}
