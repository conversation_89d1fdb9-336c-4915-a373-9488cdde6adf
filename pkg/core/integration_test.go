package core

import (
	"context"
	"math/big"
	"testing"
	"time"
	"unified-tx-parser/pkg/model"
)

// MockDexExtractor 模拟DEX提取器用于测试
type MockDexExtractor struct {
	supportedProtocols []string
	supportedChains    []ChainType
}

func NewMockDexExtractor() *MockDexExtractor {
	return &MockDexExtractor{
		supportedProtocols: []string{"uniswap", "sushiswap"},
		supportedChains:    []ChainType{ChainTypeEthereum, ChainTypeBSC},
	}
}

func (m *MockDexExtractor) GetSupportedProtocols() []string {
	return m.supportedProtocols
}

func (m *MockDexExtractor) GetSupportedChains() []ChainType {
	return m.supportedChains
}

func (m *MockDexExtractor) ExtractDexData(ctx context.Context, blocks []UnifiedBlock) (*DexData, error) {
	dexData := &DexData{
		Pools:        make([]model.Pool, 0),
		Transactions: make([]model.Transaction, 0),
		Liquidities:  make([]model.Liquidity, 0),
		Reserves:     make([]model.Reserve, 0),
		Tokens:       make([]model.Token, 0),
	}

	// 模拟从区块中提取DEX数据
	for _, block := range blocks {
		if len(block.Transactions) > 0 {
			// 创建模拟的池子
			pool := model.Pool{
				Addr:     "0xpool123",
				Protocol: "uniswap",
				Factory:  "0xfactory123",
				Tokens:   map[int]string{0: "0xtoken1", 1: "0xtoken2"},
				Fee:      3000,
				Extra: &model.PoolExtra{
					Hash: block.Transactions[0].TxHash,
					From: block.Transactions[0].FromAddress,
					Time: uint64(block.Timestamp.Unix()),
				},
			}
			dexData.Pools = append(dexData.Pools, pool)

			// 创建模拟的交易
			for _, tx := range block.Transactions {
				modelTx := model.Transaction{
					Addr:        "0xpool123",
					Hash:        tx.TxHash,
					From:        tx.FromAddress,
					Side:        "buy",
					Amount:      big.NewInt(1000),
					Price:       1.5,
					Value:       1500.0,
					Time:        uint64(tx.Timestamp.Unix()),
					EventIndex:  0,
					TxIndex:     int64(tx.TxIndex),
					BlockNumber: tx.BlockNumber.Int64(),
				}
				dexData.Transactions = append(dexData.Transactions, modelTx)
			}

			// 创建模拟的代币
			token := model.Token{
				Addr:     "0xtoken1",
				Name:     "Test Token",
				Symbol:   "TEST",
				Decimals: 18,
				IsStable: false,
			}
			dexData.Tokens = append(dexData.Tokens, token)
		}
	}

	return dexData, nil
}

func (m *MockDexExtractor) SupportsBlock(block *UnifiedBlock) bool {
	// 支持包含交易的区块
	return len(block.Transactions) > 0
}

// MockStorageEngine 模拟存储引擎用于测试
type MockStorageEngine struct {
	storedBlocks    []UnifiedBlock
	storedDexData   []*DexData
	storedTxs       []UnifiedTransaction
	storedEvents    []BusinessEvent
}

func NewMockStorageEngine() *MockStorageEngine {
	return &MockStorageEngine{
		storedBlocks:  make([]UnifiedBlock, 0),
		storedDexData: make([]*DexData, 0),
		storedTxs:     make([]UnifiedTransaction, 0),
		storedEvents:  make([]BusinessEvent, 0),
	}
}

func (m *MockStorageEngine) StoreBlocks(ctx context.Context, blocks []UnifiedBlock) error {
	m.storedBlocks = append(m.storedBlocks, blocks...)
	return nil
}

func (m *MockStorageEngine) StoreDexData(ctx context.Context, dexData *DexData) error {
	m.storedDexData = append(m.storedDexData, dexData)
	return nil
}

func (m *MockStorageEngine) StoreTransactions(ctx context.Context, txs []UnifiedTransaction) error {
	m.storedTxs = append(m.storedTxs, txs...)
	return nil
}

func (m *MockStorageEngine) StoreBusinessEvents(ctx context.Context, events []BusinessEvent) error {
	m.storedEvents = append(m.storedEvents, events...)
	return nil
}

func (m *MockStorageEngine) GetTransactionsByHash(ctx context.Context, hashes []string) ([]UnifiedTransaction, error) {
	return nil, nil
}

func (m *MockStorageEngine) GetEventsByTxHash(ctx context.Context, txHash string) ([]BusinessEvent, error) {
	return nil, nil
}

func (m *MockStorageEngine) GetEventsByType(ctx context.Context, eventType BusinessEventType, limit int) ([]BusinessEvent, error) {
	return nil, nil
}

func (m *MockStorageEngine) GetStorageStats(ctx context.Context) (map[string]interface{}, error) {
	stats := make(map[string]interface{})
	stats["blocks"] = len(m.storedBlocks)
	stats["dex_data"] = len(m.storedDexData)
	stats["transactions"] = len(m.storedTxs)
	stats["events"] = len(m.storedEvents)
	return stats, nil
}

func (m *MockStorageEngine) HealthCheck(ctx context.Context) error {
	return nil
}

// TestDataFlowIntegration 测试完整的数据流
func TestDataFlowIntegration(t *testing.T) {
	// 创建模拟组件
	chainProcessor := &MockChainProcessor{
		chainType: ChainTypeEthereum,
		chainID:   "1",
	}
	dexExtractor := NewMockDexExtractor()
	storageEngine := NewMockStorageEngine()

	// 创建引擎
	config := &EngineConfig{
		BatchSize:        10,
		ProcessInterval:  time.Second,
		MaxRetries:       3,
		ConcurrentChains: 1,
		RealTimeMode:     false,
	}
	engine := NewEngine(config)

	// 注册组件
	engine.RegisterChainProcessor(chainProcessor)
	engine.RegisterDexExtractor(dexExtractor)
	engine.SetStorageEngine(storageEngine)

	ctx := context.Background()

	// 模拟处理一个区块范围
	startBlock := big.NewInt(12345)
	endBlock := big.NewInt(12345)

	// 获取区块数据
	blocks, err := chainProcessor.GetBlocksByRange(ctx, startBlock, endBlock)
	if err != nil {
		t.Fatalf("GetBlocksByRange failed: %v", err)
	}

	if len(blocks) != 1 {
		t.Fatalf("Expected 1 block, got %d", len(blocks))
	}

	// 提取DEX数据
	dexData, err := dexExtractor.ExtractDexData(ctx, blocks)
	if err != nil {
		t.Fatalf("ExtractDexData failed: %v", err)
	}

	// 验证DEX数据
	if len(dexData.Pools) == 0 {
		t.Error("Expected at least 1 pool")
	}

	if len(dexData.Tokens) == 0 {
		t.Error("Expected at least 1 token")
	}

	// 存储数据
	if err := storageEngine.StoreBlocks(ctx, blocks); err != nil {
		t.Fatalf("StoreBlocks failed: %v", err)
	}

	if err := storageEngine.StoreDexData(ctx, dexData); err != nil {
		t.Fatalf("StoreDexData failed: %v", err)
	}

	// 验证存储结果
	stats, err := storageEngine.GetStorageStats(ctx)
	if err != nil {
		t.Fatalf("GetStorageStats failed: %v", err)
	}

	if stats["blocks"].(int) != 1 {
		t.Errorf("Expected 1 stored block, got %d", stats["blocks"].(int))
	}

	if stats["dex_data"].(int) != 1 {
		t.Errorf("Expected 1 stored dex_data, got %d", stats["dex_data"].(int))
	}

	t.Logf("Integration test passed! Stats: %+v", stats)
}

// TestEngineConfiguration 测试引擎配置
func TestEngineConfiguration(t *testing.T) {
	config := &EngineConfig{
		BatchSize:        100,
		ProcessInterval:  time.Second * 5,
		MaxRetries:       5,
		ConcurrentChains: 2,
		RealTimeMode:     true,
	}

	engine := NewEngine(config)

	if engine.config.BatchSize != 100 {
		t.Errorf("Expected BatchSize 100, got %d", engine.config.BatchSize)
	}

	if engine.config.ProcessInterval != time.Second*5 {
		t.Errorf("Expected ProcessInterval 5s, got %v", engine.config.ProcessInterval)
	}

	if engine.config.MaxRetries != 5 {
		t.Errorf("Expected MaxRetries 5, got %d", engine.config.MaxRetries)
	}

	if engine.config.ConcurrentChains != 2 {
		t.Errorf("Expected ConcurrentChains 2, got %d", engine.config.ConcurrentChains)
	}

	if !engine.config.RealTimeMode {
		t.Error("Expected RealTimeMode to be true")
	}
}
