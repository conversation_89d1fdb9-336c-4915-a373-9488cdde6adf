package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"strconv"
	"syscall"
	"time"

	"unified-tx-parser/pkg/chains/bsc"
	"unified-tx-parser/pkg/chains/ethereum"
	"unified-tx-parser/pkg/chains/solana"
	"unified-tx-parser/pkg/chains/sui"
	"unified-tx-parser/pkg/config"
	"unified-tx-parser/pkg/core"
	dex "unified-tx-parser/pkg/dexs"
	"unified-tx-parser/pkg/storage/influxdb"
	"unified-tx-parser/pkg/storage/mysql"
	redisTracker "unified-tx-parser/pkg/storage/redis"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
)

func main() {
	// 解析命令行参数
	var chainType string
	flag.StringVar(&chainType, "chain", "", "指定要运行的链类型 (sui, ethereum, bsc, solana)")
	flag.Parse()

	log.Printf("🚀 统一交易解析器启动")

	// 加载配置文件
	cfg, err := loadConfiguration(chainType)
	if err != nil {
		log.Fatalf("❌ 配置加载失败: %v", err)
	}

	// 创建引擎配置
	engineConfig := &core.EngineConfig{
		BatchSize:        cfg.Processor.BatchSize,
		ProcessInterval:  time.Duration(cfg.Processor.RetryDelay) * time.Second,
		MaxRetries:       cfg.Processor.MaxRetries,
		ConcurrentChains: cfg.Processor.MaxConcurrent,
		RealTimeMode:     true,
		ChainConfigs:     make(map[core.ChainType]*core.ChainConfig),
	}

	// 转换链配置
	for chainName, chainConfig := range cfg.Chains {
		var chainType core.ChainType
		switch chainName {
		case "sui":
			chainType = core.ChainTypeSui
		case "ethereum":
			chainType = core.ChainTypeEthereum
		case "solana":
			chainType = core.ChainTypeSolana
		default:
			continue
		}

		engineConfig.ChainConfigs[chainType] = &core.ChainConfig{
			Enabled:     chainConfig.Enabled,
			BatchSize:   chainConfig.BatchSize,
			RpcEndpoint: chainConfig.RPCEndpoint,
		}
	}

	// 创建引擎
	engine := core.NewEngine(engineConfig)

	// 初始化存储引擎
	storageEngine, err := createStorageEngine(cfg)
	if err != nil {
		log.Fatalf("❌ 存储引擎初始化失败: %v", err)
	}

	// 存储引擎会自动管理连接
	engine.SetStorageEngine(storageEngine)

	// 设置进度跟踪器 - 使用Redis进度跟踪器（生产环境）
	rdb := redis.NewClient(&redis.Options{
		Addr:       fmt.Sprintf("%s:%d", cfg.Redis.Host, cfg.Redis.Port),
		Password:   cfg.Redis.Password,
		DB:         cfg.Redis.DB,
		PoolSize:   cfg.Redis.PoolSize,
		MaxRetries: cfg.Redis.MaxRetries,
	})

	// 测试Redis连接
	if err := rdb.Ping(context.Background()).Err(); err != nil {
		log.Fatalf("❌ Redis连接失败: %v", err)
	}

	progressTracker := redisTracker.NewRedisProgressTracker(rdb, "unified_tx_parser")
	engine.SetProgressTracker(progressTracker)

	// 注册链处理器
	if err := registerChainProcessors(engine, cfg); err != nil {
		log.Fatalf("❌ 注册链处理器失败: %v", err)
	}

	// 注册DEX数据提取器（每个DEX协议独立注册）
	protocolsConfig := make(map[string]interface{})
	for name := range cfg.Protocols {
		protocolsConfig[name] = true
	}

	// 创建提取器工厂
	factory := dex.CreateFactoryWithConfig(protocolsConfig)

	// 注册所有启用的DEX提取器
	extractors := factory.GetAllExtractors()
	for _, extractor := range extractors {
		engine.RegisterDexExtractor(extractor)
	}

	// 启动引擎
	if err := engine.Start(); err != nil {
		log.Fatalf("❌ 启动引擎失败: %v", err)
	}
	defer engine.Stop()

	// 启动API服务器
	go startAPIServer(engine, storageEngine, progressTracker, cfg)

	// 等待信号
	waitForSignal()

	log.Printf("🛑 统一交易处理引擎已停止")
}

// loadConfiguration 加载配置文件
func loadConfiguration(chainType string) (*config.Config, error) {
	if chainType != "" {
		// 使用链特定配置
		log.Printf("📋 加载链特定配置: %s", chainType)
		return config.LoadChainConfig(chainType)
	}

	// 尝试从环境变量获取链类型
	envChainType := os.Getenv("CHAIN_TYPE")
	if envChainType != "" {
		log.Printf("📋 从环境变量加载链配置: %s", envChainType)
		return config.LoadChainConfig(envChainType)
	}

	// 使用传统的单体配置文件
	log.Printf("📋 加载传统配置文件")
	return config.LoadConfig("")
}

// createStorageEngine 创建存储引擎
func createStorageEngine(cfg *config.Config) (core.StorageEngine, error) {
	switch cfg.Storage.Type {
	case "mysql":
		mysqlConfig := &mysql.MySQLConfig{
			Host:         cfg.Storage.MySQL.Host,
			Port:         cfg.Storage.MySQL.Port,
			Username:     cfg.Storage.MySQL.Username,
			Password:     cfg.Storage.MySQL.Password,
			Database:     cfg.Storage.MySQL.Database,
			MaxOpenConns: cfg.Storage.MySQL.MaxOpenConns,
			MaxIdleConns: cfg.Storage.MySQL.MaxIdleConns,
		}
		storageEngine, err := mysql.NewMySQLStore(mysqlConfig)
		if err != nil {
			return nil, fmt.Errorf("MySQL存储初始化失败: %w", err)
		}
		log.Printf("💾 使用MySQL存储")
		return storageEngine, nil

	case "influxdb":
		influxConfig := &influxdb.InfluxDBConfig{
			URL:       cfg.Storage.InfluxDB.URL,
			Token:     cfg.Storage.InfluxDB.Token,
			Org:       cfg.Storage.InfluxDB.Org,
			Bucket:    cfg.Storage.InfluxDB.Bucket,
			BatchSize: cfg.Storage.InfluxDB.BatchSize,
			FlushTime: cfg.Storage.InfluxDB.FlushTime,
			Precision: cfg.Storage.InfluxDB.Precision,
		}
		storageEngine, err := influxdb.NewInfluxDBStorage(influxConfig)
		if err != nil {
			return nil, fmt.Errorf("InfluxDB存储初始化失败: %w", err)
		}
		log.Printf("💾 使用InfluxDB存储")
		return storageEngine, nil

	default:
		return nil, fmt.Errorf("不支持的存储类型: %s", cfg.Storage.Type)
	}
}

// registerChainProcessors 注册链处理器
func registerChainProcessors(engine *core.Engine, cfg *config.Config) error {
	// 注册Sui处理器
	if suiConfig, exists := cfg.Chains["sui"]; exists && suiConfig.Enabled {
		suiProcessorConfig := &sui.SuiConfig{
			RPCEndpoint: suiConfig.RPCEndpoint,
			ChainID:     suiConfig.ChainID,
			BatchSize:   suiConfig.BatchSize,
		}

		suiProcessor, err := sui.NewSuiProcessor(suiProcessorConfig)
		if err != nil {
			return fmt.Errorf("创建Sui处理器失败: %w", err)
		}

		engine.RegisterChainProcessor(suiProcessor)
	}

	// 注册以太坊处理器
	if ethConfig, exists := cfg.Chains["ethereum"]; exists && ethConfig.Enabled {
		ethProcessorConfig := &ethereum.EthereumConfig{
			RPCEndpoint: ethConfig.RPCEndpoint,
			ChainID:     1, // 以太坊主网
			BatchSize:   ethConfig.BatchSize,
			IsTestnet:   false,
		}

		ethProcessor, err := ethereum.NewEthereumProcessor(ethProcessorConfig)
		if err != nil {
			return fmt.Errorf("创建以太坊处理器失败: %w", err)
		}

		engine.RegisterChainProcessor(ethProcessor)
	}

	// 注册BSC处理器
	if bscConfig, exists := cfg.Chains["bsc"]; exists && bscConfig.Enabled {
		bscProcessorConfig := &bsc.BSCConfig{
			RPCEndpoint: bscConfig.RPCEndpoint,
			ChainID:     56, // BSC主网
			BatchSize:   bscConfig.BatchSize,
		}

		bscProcessor, err := bsc.NewBSCProcessor(bscProcessorConfig)
		if err != nil {
			return fmt.Errorf("创建BSC处理器失败: %w", err)
		}

		engine.RegisterChainProcessor(bscProcessor)
	}

	// 注册Solana处理器
	if solConfig, exists := cfg.Chains["solana"]; exists && solConfig.Enabled {
		solProcessorConfig := &solana.SolanaConfig{
			RPCEndpoint: solConfig.RPCEndpoint,
			ChainID:     solConfig.ChainID,
			BatchSize:   solConfig.BatchSize,
			IsTestnet:   false,
		}

		solProcessor, err := solana.NewSolanaProcessor(solProcessorConfig)
		if err != nil {
			return fmt.Errorf("创建Solana处理器失败: %w", err)
		}

		engine.RegisterChainProcessor(solProcessor)
	}

	return nil
}

// startAPIServer 启动API服务器
func startAPIServer(engine *core.Engine, storage core.StorageEngine, progressTracker core.ProgressTracker, cfg *config.Config) {
	gin.SetMode(gin.ReleaseMode)
	r := gin.New()
	r.Use(gin.Logger(), gin.Recovery())

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		status := map[string]interface{}{
			"status":    "ok",
			"timestamp": time.Now(),
			"engine": map[string]interface{}{
				"running": engine.IsRunning(),
			},
		}

		// 检查存储健康状态
		if err := storage.HealthCheck(c.Request.Context()); err != nil {
			status["storage"] = map[string]interface{}{
				"status": "error",
				"error":  err.Error(),
			}
		} else {
			status["storage"] = map[string]interface{}{
				"status": "ok",
			}
		}

		// 检查进度跟踪器健康状态
		if progressTracker != nil {
			if err := progressTracker.HealthCheck(); err != nil {
				status["progress_tracker"] = map[string]interface{}{
					"status": "error",
					"error":  err.Error(),
				}
			} else {
				status["progress_tracker"] = map[string]interface{}{
					"status": "ok",
					"type":   "redis",
				}
			}
		} else {
			status["progress_tracker"] = map[string]interface{}{
				"status": "not_configured",
			}
		}

		c.JSON(http.StatusOK, status)
	})

	// API路由组
	api := r.Group("/api/v1")
	{
		// 获取引擎统计
		api.GET("/stats", func(c *gin.Context) {
			stats, err := engine.GetStats(c.Request.Context())
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				return
			}
			c.JSON(http.StatusOK, stats)
		})

		// 获取处理进度
		api.GET("/progress", func(c *gin.Context) {
			if progressTracker == nil {
				c.JSON(http.StatusServiceUnavailable, gin.H{"error": "进度跟踪器未配置"})
				return
			}

			allProgress, err := progressTracker.GetAllProgress()
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				return
			}

			c.JSON(http.StatusOK, gin.H{"progress": allProgress})
		})

		// 获取全局统计
		api.GET("/progress/stats", func(c *gin.Context) {
			if progressTracker == nil {
				c.JSON(http.StatusServiceUnavailable, gin.H{"error": "进度跟踪器未配置"})
				return
			}

			globalStats, err := progressTracker.GetGlobalStats()
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				return
			}

			c.JSON(http.StatusOK, globalStats)
		})

		// 获取支持的链类型
		api.GET("/chains", func(c *gin.Context) {
			chains := make([]map[string]interface{}, 0)
			for chainName, chainConfig := range cfg.Chains {
				chains = append(chains, map[string]interface{}{
					"name":    chainName,
					"enabled": chainConfig.Enabled,
					"rpc":     chainConfig.RPCEndpoint,
				})
			}
			c.JSON(http.StatusOK, gin.H{"chains": chains})
		})

		// 根据交易哈希查询交易
		api.GET("/transactions/:hash", func(c *gin.Context) {
			hash := c.Param("hash")
			if hash == "" {
				c.JSON(http.StatusBadRequest, gin.H{"error": "交易哈希不能为空"})
				return
			}

			transactions, err := storage.GetTransactionsByHash(c.Request.Context(), []string{hash})
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				return
			}

			if len(transactions) == 0 {
				c.JSON(http.StatusNotFound, gin.H{"error": "交易未找到"})
				return
			}

			c.JSON(http.StatusOK, gin.H{"transaction": transactions[0]})
		})

		// 根据事件类型查询事件
		api.GET("/events", func(c *gin.Context) {
			eventType := c.Query("type")
			limitStr := c.DefaultQuery("limit", "10")

			limit, err := strconv.Atoi(limitStr)
			if err != nil || limit <= 0 || limit > 100 {
				limit = 10
			}

			var events []core.BusinessEvent
			if eventType != "" {
				events, err = storage.GetEventsByType(c.Request.Context(), core.BusinessEventType(eventType), limit)
			} else {
				// 获取所有类型的事件，这里简化处理
				events, err = storage.GetEventsByType(c.Request.Context(), "", limit)
			}

			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				return
			}

			c.JSON(http.StatusOK, gin.H{
				"events": events,
				"count":  len(events),
			})
		})

		// 获取存储统计
		api.GET("/storage/stats", func(c *gin.Context) {
			stats, err := storage.GetStorageStats(c.Request.Context())
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
				return
			}
			c.JSON(http.StatusOK, stats)
		})
	}

	// 启动服务器
	port := fmt.Sprintf(":%d", cfg.App.Port)
	log.Printf("🌐 API服务器启动 :%d", cfg.App.Port)
	if err := r.Run(port); err != nil {
		log.Printf("❌ 服务器启动失败: %v", err)
	}
}

// waitForSignal 等待系统信号
func waitForSignal() {
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
	<-sigChan
	log.Printf("🛑 收到停止信号")
}
